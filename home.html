<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>MoveEase - Moving Service Platform</title>
<script src="https://cdn.tailwindcss.com"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<script>
tailwind.config = {
theme: {
extend: {
colors: {
primary: '#3b82f6',
secondary: '#10b981',
dark: '#1e293b',
light: '#f8fafc'
}
}
}
}
</script>
<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
    body {
        font-family: 'Poppins', sans-serif;
        background-color: #f8fafc;
    }

    .hero-bg {
        background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('<https://images.unsplash.com/photo-1556911220-e15b29be8c8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80>');
        background-size: cover;
        background-position: center;
    }

    .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .testimonial-slider {
        scroll-behavior: smooth;
        scroll-snap-type: x mandatory;
    }

    .testimonial-slider::-webkit-scrollbar {
        display: none;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: 280px 1fr;
    }

    @media (max-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .mobile-menu {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            z-index: 50;
            background: rgba(0,0,0,0.8);
        }
    }

    .sidebar-item:hover {
        background-color: rgba(59, 130, 246, 0.1);
    }

    .sidebar-item.active {
        background-color: rgba(59, 130, 246, 0.2);
        border-left: 4px solid #3b82f6;
    }

    .stat-card {
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: scale(1.03);
    }

    .move-card {
        transition: all 0.3s ease;
    }

    .move-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }

    .chart-bar {
        transition: height 0.5s ease-in-out;
    }
</style>
</head>
<body class="bg-gray-50">
<!-- Navbar -->
<nav class="bg-white shadow-md fixed w-full z-10">
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
<div class="flex justify-between h-16">
<div class="flex items-center">
<div class="flex-shrink-0 flex items-center">
<i class="fas fa-truck-moving text-primary text-2xl mr-2"></i>
<span class="text-xl font-bold text-dark">Move<span class="text-primary">Ease</span></span>
</div>
</div>
<div class="hidden md:flex items-center space-x-8">
<a href="#" class="text-gray-600 hover:text-primary transition">Home</a>
<a href="#" class="text-gray-600 hover:text-primary transition">How It Works</a>
<a href="#" class="text-gray-600 hover:text-primary transition">For Movers</a>
<a href="#" class="text-gray-600 hover:text-primary transition">Contact</a>
</div>
<div class="hidden md:flex items-center space-x-4">
<button onclick="showPage('login-page')" class="px-4 py-2 rounded-md text-primary font-medium hover:bg-blue-50 transition">Log In</button>
<button onclick="showPage('signup-page')" class="px-4 py-2 rounded-md bg-primary text-white font-medium hover:bg-blue-600 transition">Sign Up</button>
</div>
<div class="md:hidden flex items-center">
<button id="mobile-menu-button" class="text-gray-600">
<i class="fas fa-bars text-xl"></i>
</button>
</div>
</div>
</div>
</nav>
<!-- Mobile Menu -->
<div id="mobile-menu" class="mobile-menu hidden md:hidden">
    <div class="bg-white w-3/4 h-full p-6">
        <div class="flex justify-between items-center mb-8">
            <div class="flex items-center">
                <i class="fas fa-truck-moving text-primary text-2xl mr-2"></i>
                <span class="text-xl font-bold text-dark">Move<span class="text-primary">Ease</span></span>
            </div>
            <button id="close-menu" class="text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="space-y-4">
            <a href="#" class="block text-gray-600 hover:text-primary transition py-2">Home</a>
            <a href="#" class="block text-gray-600 hover:text-primary transition py-2">How It Works</a>
            <a href="#" class="block text-gray-600 hover:text-primary transition py-2">For Movers</a>
            <a href="#" class="block text-gray-600 hover:text-primary transition py-2">Contact</a>
            <div class="pt-4 border-t">
                <button onclick="showPage('login-page')" class="w-full mb-3 px-4 py-2 rounded-md text-primary font-medium hover:bg-blue-50 transition">Log In</button>
                <button onclick="showPage('signup-page')" class="w-full px-4 py-2 rounded-md bg-primary text-white font-medium hover:bg-blue-600 transition">Sign Up</button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="pt-16">
    <!-- Homepage -->
    <section id="home-page">
        <!-- Hero Section -->
        <div class="hero-bg py-24 px-4 text-white">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">Stress-Free Moving Experience</h1>
                <p class="text-xl mb-8">Connect with trusted movers, get competitive quotes, and enjoy a seamless relocation</p>
                <button onclick="showPage('signup-page')" class="bg-primary hover:bg-blue-600 text-white font-bold py-3 px-8 rounded-full text-lg transition transform hover:scale-105">
                    Book Your Move Today
                </button>
            </div>
        </div>

        <!-- How It Works -->
        <div class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-dark mb-12">How It Works</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center p-6 card-hover transition">
                        <div class="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-map-marker-alt text-primary text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">1. Post Your Move</h3>
                        <p class="text-gray-600">Tell us about your move - locations, date, and requirements.</p>
                    </div>
                    <div class="text-center p-6 card-hover transition">
                        <div class="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-truck text-secondary text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">2. Receive Quotes</h3>
                        <p class="text-gray-600">Get competitive offers from professional moving companies.</p>
                    </div>
                    <div class="text-center p-6 card-hover transition">
                        <div class="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-calendar-check text-primary text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold mb-3">3. Book & Move</h3>
                        <p class="text-gray-600">Choose your mover, confirm booking, and enjoy a stress-free move.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="py-16 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-dark mb-12">Why Choose MoveEase</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                        <i class="fas fa-shield-alt text-primary text-3xl mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Verified Movers</h3>
                        <p class="text-gray-600">All movers are background-checked and licensed for your safety.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                        <i class="fas fa-tag text-secondary text-3xl mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Competitive Pricing</h3>
                        <p class="text-gray-600">Get multiple quotes to find the best price for your move.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                        <i class="fas fa-headset text-primary text-3xl mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">24/7 Support</h3>
                        <p class="text-gray-600">Our team is always available to assist with your move.</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                        <i class="fas fa-file-contract text-secondary text-3xl mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Insurance Coverage</h3>
                        <p class="text-gray-600">All moves include basic insurance coverage for peace of mind.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonials -->
        <div class="py-16 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-3xl font-bold text-center text-dark mb-12">What Our Customers Say</h2>
                <div class="flex overflow-x-auto pb-8 testimonial-slider scroll-snap" style="scroll-snap-type: x mandatory;">
                    <div class="flex-none w-80 md:w-96 scroll-snap-align-start mr-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">JD</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold">John D.</h4>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600">"Moving was a breeze with MoveEase. I got 5 quotes within hours and saved 30% compared to other services. The movers were professional and efficient."</p>
                        </div>
                    </div>
                    <div class="flex-none w-80 md:w-96 scroll-snap-align-start mr-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">SM</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Sarah M.</h4>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star-half-alt"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600">"As a single parent moving with two kids, I was dreading the process. MoveEase made it so simple! The movers handled everything with care and were great with my children."</p>
                        </div>
                    </div>
                    <div class="flex-none w-80 md:w-96 scroll-snap-align-start mr-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center mr-4">
                                    <span class="text-white font-bold">RT</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Robert T.</h4>
                                    <div class="flex text-yellow-400">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="text-gray-600">"I've used MoveEase for both home and office moves. Their platform is intuitive and the movers they connect you with are top-notch professionals. Highly recommend!"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Become a Mover -->
        <div class="py-16 bg-gradient-to-r from-primary to-secondary">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="md:flex items-center justify-between">
                    <div class="md:w-1/2 mb-8 md:mb-0">
                        <h2 class="text-3xl font-bold text-white mb-4">Become a MoveEase Partner</h2>
                        <p class="text-blue-100 mb-6">Join our network of professional movers and grow your business with quality leads.</p>
                        <ul class="text-blue-100 space-y-2">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle mt-1 mr-2"></i>
                                <span>Access thousands of moving requests monthly</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle mt-1 mr-2"></i>
                                <span>Set your own rates and availability</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle mt-1 mr-2"></i>
                                <span>Get paid securely through our platform</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle mt-1 mr-2"></i>
                                <span>Marketing support to grow your business</span>
                            </li>
                        </ul>
                    </div>
                    <div class="md:w-2/5">
                        <div class="bg-white rounded-lg p-6 shadow-lg">
                            <h3 class="text-xl font-semibold mb-4 text-center">Start Earning Today</h3>
                            <button onclick="showPage('signup-page')" class="w-full bg-primary hover:bg-blue-600 text-white font-medium py-3 rounded-md transition">Sign Up as Mover</button>
                            <p class="text-center text-gray-600 mt-4 text-sm">Already have an account? <button onclick="showPage('login-page')" class="text-primary font-medium">Log In</button></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Authentication Pages -->
    <!-- Login Page -->
    <section id="login-page" class="hidden py-16 bg-gray-50 min-h-screen">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-primary p-6 text-center">
                <h2 class="text-2xl font-bold text-white">Welcome Back</h2>
                <p class="text-blue-100">Sign in to your account</p>
            </div>
            <div class="p-6">
                <form>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="email">Email</label>
                        <input type="email" id="email" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="password">Password</label>
                        <input type="password" id="password" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="remember" class="mr-2">
                            <label for="remember" class="text-gray-700">Remember me</label>
                        </div>
                        <button type="button" onclick="showPage('reset-password-page')" class="text-primary font-medium">Forgot password?</button>
                    </div>
                    <button type="button" onclick="showPage('customer-dashboard')" class="w-full bg-primary hover:bg-blue-600 text-white font-medium py-2 rounded-md transition mb-4">Sign In</button>
                    <div class="text-center">
                        <p class="text-gray-600">Don't have an account? <button type="button" onclick="showPage('signup-page')" class="text-primary font-medium">Sign Up</button></p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Signup Page -->
    <section id="signup-page" class="hidden py-16 bg-gray-50 min-h-screen">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-primary p-6 text-center">
                <h2 class="text-2xl font-bold text-white">Create an Account</h2>
                <p class="text-blue-100">Join MoveEase in just a few steps</p>
            </div>
            <div class="p-6">
                <div class="flex border-b mb-6">
                    <button class="flex-1 py-2 px-4 text-center font-medium border-b-2 border-primary text-primary">Customer</button>
                    <button class="flex-1 py-2 px-4 text-center font-medium text-gray-500">Mover</button>
                </div>
                <form>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="fullname">Full Name</label>
                        <input type="text" id="fullname" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="signup-email">Email</label>
                        <input type="email" id="signup-email" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="signup-password">Password</label>
                        <input type="password" id="signup-password" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 mb-2" for="confirm-password">Confirm Password</label>
                        <input type="password" id="confirm-password" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <div class="mb-6">
                        <div class="flex items-center">
                            <input type="checkbox" id="terms" class="mr-2">
                            <label for="terms" class="text-gray-700">I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a></label>
                        </div>
                    </div>
                    <button type="button" onclick="showPage('customer-dashboard')" class="w-full bg-primary hover:bg-blue-600 text-white font-medium py-2 rounded-md transition mb-4">Create Account</button>
                    <div class="text-center">
                        <p class="text-gray-600">Already have an account? <button type="button" onclick="showPage('login-page')" class="text-primary font-medium">Log In</button></p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Reset Password Page -->
    <section id="reset-password-page" class="hidden py-16 bg-gray-50 min-h-screen">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-primary p-6 text-center">
                <h2 class="text-2xl font-bold text-white">Reset Password</h2>
                <p class="text-blue-100">Enter your email to reset your password</p>
            </div>
            <div class="p-6">
                <form>
                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2" for="reset-email">Email</label>
                        <input type="email" id="reset-email" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>
                    <button type="button" class="w-full bg-primary hover:bg-blue-600 text-white font-medium py-2 rounded-md transition mb-4">Send Reset Link</button>
                    <div class="text-center">
                        <button type="button" onclick="showPage('login-page')" class="text-primary font-medium">Back to Login</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Customer Dashboard -->
    <section id="customer-dashboard" class="hidden min-h-screen">
        <div class="dashboard-grid">
            <!-- Sidebar -->
            <div class="bg-dark text-white h-full fixed w-72 hidden md:block">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center mr-3">
                            <span class="text-white font-bold">JD</span>
                        </div>
                        <div>
                            <h3 class="font-semibold">John Doe</h3>
                            <p class="text-gray-400 text-sm">Customer</p>
                        </div>
                    </div>
                </div>
                <nav class="p-4">
                    <ul class="space-y-1">
                        <li><a href="#" class="sidebar-item active block py-3 px-4 rounded-md"><i class="fas fa-home mr-3"></i> Dashboard</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-list mr-3"></i> My Requests</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-calendar-alt mr-3"></i> Bookings</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-comments mr-3"></i> Messages</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-cog mr-3"></i> Settings</a></li>
                        <li><button onclick="showPage('home-page')" class="w-full text-left sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-sign-out-alt mr-3"></i> Logout</button></li>
                    </ul>
                </nav>
            </div>

            <!-- Mobile Sidebar Toggle -->
            <div class="md:hidden bg-white shadow-md p-4 flex justify-between items-center">
                <button id="dashboard-menu-toggle" class="text-gray-600">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                        <span class="text-gray-700 font-bold">JD</span>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="md:ml-72 p-4 md:p-8">
                <div class="mb-8">
                    <h1 class="text-2xl font-bold text-dark">Customer Dashboard</h1>
                    <p class="text-gray-600">Welcome back, John! Here's an overview of your moving activities.</p>
                </div>

                <!-- Action Card -->
                <div class="bg-gradient-to-r from-primary to-secondary rounded-lg p-6 text-white mb-8">
                    <div class="flex flex-col md:flex-row items-center justify-between">
                        <div class="mb-4 md:mb-0">
                            <h2 class="text-xl font-bold mb-2">Need to move?</h2>
                            <p class="opacity-90">Post your move request and get quotes from movers</p>
                        </div>
                        <button onclick="showPage('post-move-page')" class="bg-white text-primary font-semibold py-2 px-6 rounded-full hover:bg-gray-100 transition">Post a Move Request</button>
                    </div>
                </div>

                <!-- Recent Requests -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-dark">Recent Move Requests</h2>
                        <button class="text-primary font-medium">View All</button>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-50 text-gray-600 text-left">
                                    <th class="py-3 px-4">Request ID</th>
                                    <th class="py-3 px-4">From/To</th>
                                    <th class="py-3 px-4">Date</th>
                                    <th class="py-3 px-4">Status</th>
                                    <th class="py-3 px-4">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="py-3 px-4">#REQ-0012</td>
                                    <td class="py-3 px-4">New York → Boston</td>
                                    <td class="py-3 px-4">Jun 15, 2023</td>
                                    <td class="py-3 px-4"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Completed</span></td>
                                    <td class="py-3 px-4">
                                        <button class="text-primary hover:text-blue-600 mr-2"><i class="fas fa-eye"></i></button>
                                        <button class="text-gray-600 hover:text-gray-900"><i class="fas fa-ellipsis-v"></i></button>
                                    </td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-3 px-4">#REQ-0011</td>
                                    <td class="py-3 px-4">Chicago → Detroit</td>
                                    <td class="py-3 px-4">Jun 10, 2023</td>
                                    <td class="py-3 px-4"><span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">In Progress</span></td>
                                    <td class="py-3 px-4">
                                        <button class="text-primary hover:text-blue-600 mr-2"><i class="fas fa-eye"></i></button>
                                        <button class="text-gray-600 hover:text-gray-900"><i class="fas fa-ellipsis-v"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-3 px-4">#REQ-0010</td>
                                    <td class="py-3 px-4">Seattle → Portland</td>
                                    <td class="py-3 px-4">Jun 5, 2023</td>
                                    <td class="py-3 px-4"><span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Pending Quotes</span></td>
                                    <td class="py-3 px-4">
                                        <button class="text-primary hover:text-blue-600 mr-2"><i class="fas fa-eye"></i></button>
                                        <button class="text-gray-600 hover:text-gray-900"><i class="fas fa-ellipsis-v"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Upcoming Bookings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-dark mb-6">Upcoming Bookings</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="border rounded-lg p-4 move-card">
                            <div class="flex justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold">Moving to Boston</h3>
                                    <p class="text-sm text-gray-600">#BOOK-0045</p>
                                </div>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Confirmed</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-3">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Jun 15, 2023</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-4">
                                <i class="fas fa-truck mr-2"></i>
                                <span>Swift Movers Inc.</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-bold">$850</span>
                                <button class="text-primary hover:text-blue-600 text-sm font-medium">View Details</button>
                            </div>
                        </div>
                        <div class="border rounded-lg p-4 move-card">
                            <div class="flex justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold">Office Relocation</h3>
                                    <p class="text-sm text-gray-600">#BOOK-0042</p>
                                </div>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">Pending</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-3">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Jun 20, 2023</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-4">
                                <i class="fas fa-truck mr-2"></i>
                                <span>City Movers LLC</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-bold">$1,200</span>
                                <button class="text-primary hover:text-blue-600 text-sm font-medium">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Post Move Request Page -->
    <section id="post-move-page" class="hidden min-h-screen">
        <div class="dashboard-grid">
            <!-- Sidebar (same as customer dashboard) -->
            <div class="bg-dark text-white h-full fixed w-72 hidden md:block">
                <!-- ... same as customer dashboard sidebar ... -->
            </div>

            <!-- Main Content -->
            <div class="md:ml-72 p-4 md:p-8">
                <div class="mb-8">
                    <button onclick="showPage('customer-dashboard')" class="text-primary mb-4 flex items-center">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
                    </button>
                    <h1 class="text-2xl font-bold text-dark">Post a Move Request</h1>
                    <p class="text-gray-600">Fill in the details of your move to get quotes from movers</p>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6">
                    <form>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label class="block text-gray-700 mb-2" for="from-city">Moving From</label>
                                <select id="from-city" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">Select City</option>
                                    <option value="ny">New York, NY</option>
                                    <option value="la">Los Angeles, CA</option>
                                    <option value="ch">Chicago, IL</option>
                                    <option value="sf">San Francisco, CA</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2" for="to-city">Moving To</label>
                                <select id="to-city" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="">Select City</option>
                                    <option value="ny">New York, NY</option>
                                    <option value="la">Los Angeles, CA</option>
                                    <option value="ch">Chicago, IL</option>
                                    <option value="sf">San Francisco, CA</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-gray-700 mb-2" for="move-date">Move Date</label>
                            <input type="date" id="move-date" class="w-full md:w-1/2 px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>

                        <div class="mb-6">
                            <label class="block text-gray-700 mb-2">Move Type</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <label class="flex items-center border rounded-md p-4 cursor-pointer hover:border-primary">
                                    <input type="radio" name="move-type" class="mr-3 text-primary focus:ring-primary">
                                    <div>
                                        <h3 class="font-medium">Apartment</h3>
                                        <p class="text-sm text-gray-600">Studio, 1-2 bedrooms</p>
                                    </div>
                                </label>
                                <label class="flex items-center border rounded-md p-4 cursor-pointer hover:border-primary">
                                    <input type="radio" name="move-type" class="mr-3 text-primary focus:ring-primary">
                                    <div>
                                        <h3 class="font-medium">House</h3>
                                        <p class="text-sm text-gray-600">3+ bedrooms, townhouse</p>
                                    </div>
                                </label>
                                <label class="flex items-center border rounded-md p-4 cursor-pointer hover:border-primary">
                                    <input type="radio" name="move-type" class="mr-3 text-primary focus:ring-primary">
                                    <div>
                                        <h3 class="font-medium">Office</h3>
                                        <p class="text-sm text-gray-600">Commercial relocation</p>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label class="block text-gray-700 mb-2" for="description">Description</label>
                            <textarea id="description" rows="4" class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Provide details about your move..."></textarea>
                        </div>

                        <div class="mb-8">
                            <label class="block text-gray-700 mb-2">Additional Services</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3 rounded text-primary focus:ring-primary">
                                    <span>Packing Service</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3 rounded text-primary focus:ring-primary">
                                    <span>Furniture Disassembly</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3 rounded text-primary focus:ring-primary">
                                    <span>Storage Solutions</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-3 rounded text-primary focus:ring-primary">
                                    <span>Cleaning Service</span>
                                </label>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="button" onclick="showPage('customer-dashboard')" class="bg-primary hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition">Submit Request</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Mover Dashboard -->
    <section id="mover-dashboard" class="hidden min-h-screen">
        <div class="dashboard-grid">
            <!-- Sidebar -->
            <div class="bg-dark text-white h-full fixed w-72 hidden md:block">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center mr-3">
                            <span class="text-white font-bold">SM</span>
                        </div>
                        <div>
                            <h3 class="font-semibold">Swift Movers Inc.</h3>
                            <p class="text-gray-400 text-sm">Mover</p>
                        </div>
                    </div>
                </div>
                <nav class="p-4">
                    <ul class="space-y-1">
                        <li><a href="#" class="sidebar-item active block py-3 px-4 rounded-md"><i class="fas fa-home mr-3"></i> Dashboard</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-building mr-3"></i> Service Profile</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-list mr-3"></i> Available Requests</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-calendar-alt mr-3"></i> My Bookings</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-comments mr-3"></i> Messages</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-star mr-3"></i> Reviews</a></li>
                        <li><button onclick="showPage('home-page')" class="w-full text-left sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-sign-out-alt mr-3"></i> Logout</button></li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="md:ml-72 p-4 md:p-8">
                <div class="mb-8">
                    <h1 class="text-2xl font-bold text-dark">Mover Dashboard</h1>
                    <p class="text-gray-600">Welcome back, Swift Movers! Here's your business overview.</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Total Bookings</p>
                                <h2 class="text-2xl font-bold">24</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                                <i class="fas fa-calendar-check text-primary text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-3">
                            <p class="text-green-600 text-sm"><i class="fas fa-arrow-up mr-1"></i> 12% from last month</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Pending Requests</p>
                                <h2 class="text-2xl font-bold">5</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                                <i class="fas fa-clock text-yellow-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Avg. Rating</p>
                                <h2 class="text-2xl font-bold">4.8</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="fas fa-star text-green-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Revenue</p>
                                <h2 class="text-2xl font-bold">$8,240</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-purple-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Requests -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-dark">Available Move Requests</h2>
                        <div class="flex space-x-2">
                            <select class="border rounded-md px-3 py-1 text-sm">
                                <option>All Cities</option>
                                <option>New York</option>
                                <option>Los Angeles</option>
                            </select>
                            <select class="border rounded-md px-3 py-1 text-sm">
                                <option>All Types</option>
                                <option>Apartment</option>
                                <option>House</option>
                                <option>Office</option>
                            </select>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="border rounded-lg p-4 move-card">
                            <div class="flex justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold">Moving to Boston</h3>
                                    <p class="text-sm text-gray-600">From: New York, NY</p>
                                </div>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">New</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-3">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Jun 15, 2023</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-4">
                                <i class="fas fa-home mr-2"></i>
                                <span>2-Bedroom Apartment</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-600">Budget:</span>
                                    <span class="font-bold ml-2">$700-$900</span>
                                </div>
                                <button class="bg-primary hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md text-sm transition">Send Offer</button>
                            </div>
                        </div>
                        <div class="border rounded-lg p-4 move-card">
                            <div class="flex justify-between mb-3">
                                <div>
                                    <h3 class="font-semibold">Office Relocation</h3>
                                    <p class="text-sm text-gray-600">From: Chicago, IL</p>
                                </div>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">Urgent</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-3">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Jun 18, 2023</span>
                            </div>
                            <div class="flex items-center text-gray-600 mb-4">
                                <i class="fas fa-building mr-2"></i>
                                <span>Small Office (800 sq ft)</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-600">Budget:</span>
                                    <span class="font-bold ml-2">$1,000-$1,500</span>
                                </div>
                                <button class="bg-primary hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md text-sm transition">Send Offer</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Bookings -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-dark mb-6">Recent Bookings</h2>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-gray-50 text-gray-600 text-left">
                                    <th class="py-3 px-4">Booking ID</th>
                                    <th class="py-3 px-4">Customer</th>
                                    <th class="py-3 px-4">Date</th>
                                    <th class="py-3 px-4">Amount</th>
                                    <th class="py-3 px-4">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b">
                                    <td class="py-3 px-4">#BOOK-0045</td>
                                    <td class="py-3 px-4">John Doe</td>
                                    <td class="py-3 px-4">Jun 15, 2023</td>
                                    <td class="py-3 px-4">$850</td>
                                    <td class="py-3 px-4"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Completed</span></td>
                                </tr>
                                <tr class="border-b">
                                    <td class="py-3 px-4">#BOOK-0043</td>
                                    <td class="py-3 px-4">Sarah Johnson</td>
                                    <td class="py-3 px-4">Jun 12, 2023</td>
                                    <td class="py-3 px-4">$1,200</td>
                                    <td class="py-3 px-4"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Completed</span></td>
                                </tr>
                                <tr>
                                    <td class="py-3 px-4">#BOOK-0042</td>
                                    <td class="py-3 px-4">Michael Brown</td>
                                    <td class="py-3 px-4">Jun 20, 2023</td>
                                    <td class="py-3 px-4">$950</td>
                                    <td class="py-3 px-4"><span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Upcoming</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Admin Dashboard -->
    <section id="admin-dashboard" class="hidden min-h-screen">
        <div class="dashboard-grid">
            <!-- Sidebar -->
            <div class="bg-dark text-white h-full fixed w-72 hidden md:block">
                <div class="p-6 border-b border-gray-700">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center mr-3">
                            <span class="text-white font-bold">A</span>
                        </div>
                        <div>
                            <h3 class="font-semibold">Admin User</h3>
                            <p class="text-gray-400 text-sm">Administrator</p>
                        </div>
                    </div>
                </div>
                <nav class="p-4">
                    <ul class="space-y-1">
                        <li><a href="#" class="sidebar-item active block py-3 px-4 rounded-md"><i class="fas fa-chart-bar mr-3"></i> Overview</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-users mr-3"></i> Manage Users</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-list mr-3"></i> Bookings</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-truck mr-3"></i> Movers</a></li>
                        <li><a href="#" class="sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-cog mr-3"></i> Settings</a></li>
                        <li><button onclick="showPage('home-page')" class="w-full text-left sidebar-item block py-3 px-4 rounded-md"><i class="fas fa-sign-out-alt mr-3"></i> Logout</button></li>
                    </ul>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="md:ml-72 p-4 md:p-8">
                <div class="mb-8">
                    <h1 class="text-2xl font-bold text-dark">Admin Dashboard</h1>
                    <p class="text-gray-600">System overview and management</p>
                </div>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Total Users</p>
                                <h2 class="text-2xl font-bold">1,248</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                                <i class="fas fa-users text-primary text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Active Bookings</p>
                                <h2 class="text-2xl font-bold">86</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                                <i class="fas fa-calendar-check text-green-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Mover Companies</p>
                                <h2 class="text-2xl font-bold">42</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                                <i class="fas fa-truck text-yellow-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6 stat-card">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-gray-600">Revenue</p>
                                <h2 class="text-2xl font-bold">$24,580</h2>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-purple-500 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-6">Monthly Earnings</h2>
                        <div class="flex items-end h-64 space-x-2">
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 40%;"></div>
                                <span class="mt-2 text-sm text-gray-600">Jan</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 60%;"></div>
                                <span class="mt-2 text-sm text-gray-600">Feb</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 80%;"></div>
                                <span class="mt-2 text-sm text-gray-600">Mar</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 70%;"></div>
                                <span class="mt-2 text-sm text-gray-600">Apr</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 90%;"></div>
                                <span class="mt-2 text-sm text-gray-600">May</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center">
                                <div class="chart-bar bg-primary w-full max-w-12" style="height: 75%;"></div>
                                <span class="mt-2 text-sm text-gray-600">Jun</span>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold mb-6">User Statistics</h2>
                        <div class="flex justify-center">
                            <div class="w-64 h-64 relative">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div>
                                        <p class="text-center font-bold">Total Users</p>
                                        <p class="text-center text-2xl">1,248</p>
                                    </div>
                                </div>
                                <svg viewBox="0 0 36 36" class="circular-chart">
                                    <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#eee" stroke-width="3"></path>
                                    <path class="circle customers" stroke-dasharray="60, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#3b82f6" stroke-width="3"></path>
                                    <path class="circle movers" stroke-dasharray="30, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#10b981" stroke-width="3"></path>
                                    <path class="circle admins" stroke-dasharray="10, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none" stroke="#8b5cf6" stroke-width="3"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex justify-center mt-4">
                            <div class="flex space-x-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-primary rounded-full mr-2"></div>
                                    <span>Customers</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span>Movers</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                    <span>Admins</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold mb-6">Recent Activity</h2>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-user-plus text-primary"></i>
                            </div>
                            <div>
                                <p class="font-medium">New customer registration</p>
                                <p class="text-gray-600 text-sm">John Doe registered as a customer</p>
                                <p class="text-gray-500 text-xs">2 hours ago</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-4">
                                <i class="fas fa-check-circle text-green-500"></i>
                            </div>
                            <div>
                                <p class="font-medium">Booking completed</p>
                                <p class="text-gray-600 text-sm">Swift Movers completed booking #BOOK-0045</p>
                                <p class="text-gray-500 text-xs">5 hours ago</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center mr-4">
                                <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                            </div>
                            <div>
                                <p class="font-medium">Mover approval needed</p>
                                <p class="text-gray-600 text-sm">City Movers LLC submitted documents for verification</p>
                                <p class="text-gray-500 text-xs">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Confirmation Page -->
    <section id="booking-confirmation-page" class="hidden min-h-screen py-16">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-primary p-6 text-center">
                <h2 class="text-2xl font-bold text-white">Booking Confirmed!</h2>
                <p class="text-blue-100">Your move is scheduled successfully</p>
            </div>
            <div class="p-6">
                <div class="flex flex-col md:flex-row justify-between mb-8">
                    <div class="mb-6 md:mb-0">
                        <h3 class="text-lg font-semibold mb-2">Booking Details</h3>
                        <div class="space-y-2">
                            <div class="flex">
                                <span class="w-32 text-gray-600">Booking ID:</span>
                                <span class="font-medium">#BOOK-0045</span>
                            </div>
                            <div class="flex">
                                <span class="w-32 text-gray-600">Move Date:</span>
                                <span class="font-medium">June 15, 2023</span>
                            </div>
                            <div class="flex">
                                <span class="w-32 text-gray-600">From:</span>
                                <span class="font-medium">New York, NY</span>
                            </div>
                            <div class="flex">
                                <span class="w-32 text-gray-600">To:</span>
                                <span class="font-medium">Boston, MA</span>
                            </div>
                            <div class="flex">
                                <span class="w-32 text-gray-600">Move Type:</span>
                                <span class="font-medium">2-Bedroom Apartment</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold mb-2">Mover Information</h3>
                        <div class="flex items-center mb-4">
                            <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                                <span class="text-gray-700 font-bold">SM</span>
                            </div>
                            <div>
                                <h4 class="font-semibold">Swift Movers Inc.</h4>
                                <div class="flex text-yellow-400">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star-half-alt"></i>
                                    <span class="text-gray-600 ml-2">4.7 (128 reviews)</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-phone mr-2"></i>
                            <span>(*************</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <i class="fas fa-envelope mr-2"></i>
                            <span><EMAIL></span>
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-lg font-semibold mb-4">Price Breakdown</h3>
                    <div class="border rounded-lg">
                        <div class="border-b p-4 flex justify-between">
                            <span>Base Moving Fee</span>
                            <span>$650.00</span>
                        </div>
                        <div class="border-b p-4 flex justify-between">
                            <span>Packing Service</span>
                            <span>$120.00</span>
                        </div>
                        <div class="border-b p-4 flex justify-between">
                            <span>Furniture Disassembly</span>
                            <span>$80.00</span>
                        </div>
                        <div class="p-4 flex justify-between font-bold">
                            <span>Total</span>
                            <span>$850.00</span>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row justify-between items-center">
                    <button class="bg-primary hover:bg-blue-600 text-white font-medium py-3 px-8
</html>